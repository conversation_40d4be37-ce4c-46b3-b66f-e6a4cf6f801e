// <PERSON>ript to run the delivery note migration for both quotations and orders
// This should be executed to add deliveryNote field to existing records

console.log('=== DELIVERY NOTE MIGRATION SCRIPT ===\n');

console.log('Bu script aşağıdaki işlemleri yapacak:');
console.log('1. Mevcut purchase quotation items\'lara deliveryNote alanı ekleyecek');
console.log('2. Mevcut purchase order items\'lara deliveryNote alanı ekleyecek');
console.log('3. Convert işleminin düzgün çalışmasını sağlayacak\n');

console.log('Migration\'ları çalıştırmak için aşağıdaki RPC çağrılarını yapın:\n');

console.log('1. Quotation migration:');
console.log('   app.rpc(\'purchase.migrate-add-delivery-note-to-quotations\', {}, {user: currentUser});\n');

console.log('2. Order migration:');
console.log('   app.rpc(\'purchase.migrate-add-delivery-note-to-orders\', {}, {user: currentUser});\n');

console.log('Bu migration\'lar çalıştırıldıktan sonra:');
console.log('- Quotation\'larda items içinde deliveryNote alanı görünecek');
console.log('- Order\'larda items içinde deliveryNote alanı görünecek');
console.log('- Convert işlemi teslimat notlarını koruyacak');

console.log('\n=== MANUEL TEST ADIMI ===\n');
console.log('Migration\'dan sonra test etmek için:');
console.log('1. Bir quotation oluşturun');
console.log('2. Items\'lara deliveryNote ekleyin');
console.log('3. Convert to Order yapın');
console.log('4. Order\'da deliveryNote\'ların geldiğini kontrol edin');

console.log('\n=== VERITABANI KONTROL ===\n');
console.log('MongoDB\'de kontrol etmek için:');
console.log('db.purchase_quotations.findOne({"items.0": {$exists: true}}, {"items": 1})');
console.log('db.purchase_orders.findOne({"items.0": {$exists: true}}, {"items": 1})');
