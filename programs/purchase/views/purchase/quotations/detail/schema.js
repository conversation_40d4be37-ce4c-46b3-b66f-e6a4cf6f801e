import _ from 'lodash';
import StockStatusCellRenderer from './_stock-status-cell-renderer';
import TaxCellRenderer from './_tax-cell-renderer';
import DiscountCellRenderer from './_discount-cell-renderer';

export default function (vm) {
    const currenciesMap = {};
    for (const currency of vm.currencies || []) {
        currenciesMap[currency._id] = currency;
    }

    return {
        // General
        status: {
            type: 'string',
            label: 'Status',
            allowed: ['draft', 'payment-planned', 'converted-to-order', 'converted-to-invoice', 'canceled'],
            default: 'draft'
        },
        documentTypeId: {
            type: 'string',
            label: 'Document type'
        },
        code: {
            type: 'string',
            label: 'Code'
        },
        reference: {
            type: 'string',
            label: 'Reference',
            index: true,
            required: false
        },
        partnerGroupId: {
            type: 'string',
            label: 'Vendor group',
            required: false
        },
        partnerId: {
            type: 'string',
            label: 'Vendor'
        },
        currencyId: {
            type: 'string',
            label: 'Currency'
        },
        currencyRate: {
            type: 'decimal',
            label: 'Currency rate',
            default: 1
        },
        contactPersonId: {
            type: 'string',
            label: 'Contact person',
            required: false
        },
        recordDate: {
            type: 'date',
            label: 'Record date',
            default: 'date:now'
        },
        quotationDate: {
            type: 'date',
            label: 'Quotation date',
            default: 'date:now'
        },
        expiryDate: {
            type: 'date',
            label: 'Expiry date',
            default: 'date:now'
        },
        scheduledDate: {
            type: 'date',
            label: 'Delivery date',
            default: 'date:now'
        },
        branchId: {
            type: 'string',
            label: 'Branch office'
        },
        note: {
            type: 'string',
            label: 'Note',
            required: false
        },

        // Totals
        subTotal: {
            type: 'decimal',
            label: 'Subtotal',
            default: 0
        },
        discount: {
            type: 'decimal',
            label: 'Discount %',
            default: 0
        },
        discountAmount: {
            type: 'decimal',
            label: 'Discount',
            default: 0
        },
        subTotalAfterDiscount: {
            type: 'decimal',
            label: 'Subtotal after discount',
            default: 0
        },
        taxTotal: {
            type: 'decimal',
            label: 'Tax total',
            default: 0
        },
        rounding: {
            type: 'decimal',
            label: 'Rounding',
            default: 0
        },
        grandTotal: {
            type: 'decimal',
            label: 'Grand total',
            default: 0
        },
        appliedTaxes: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        },

        // Items
        items: [
            {
                id: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                stockStatus: {
                    type: 'string',
                    required: false,
                    column: {
                        iconColumn: true,
                        pinned: 'left',
                        cellRendererFramework: StockStatusCellRenderer
                    }
                },
                productId: {
                    type: 'string',
                    label: 'Product',
                    required: false,
                    column: {
                        populate: 'product',
                        pinned: 'left',
                        visible: false,
                        minWidth: 180
                    },
                    editor: {
                        collection: 'inventory.products',
                        view: 'inventory.catalog.products',
                        labelFrom: 'displayName',
                        filters() {
                            const filters = {
                                isSimple: true,
                                canBePurchased: true
                            };

                            if (_.isObject(vm.organizationSettings) && !_.isEmpty(vm.organizationSettings)) {
                                filters.groupIds = {
                                    $in: vm.organizationSettings.productGroupIds || []
                                };
                            }

                            return filters;
                        },
                        updateParams(params, type) {
                            if (type === 'create' || type === 'detail') {
                                params.model = {
                                    isSimple: true,
                                    canBePurchased: true
                                };

                                if (
                                    _.isObject(vm.organizationSettings) &&
                                    !_.isEmpty(vm.organizationSettings) &&
                                    vm.organizationSettings.defaultProductGroupId
                                ) {
                                    params.model.groupIds = [vm.organizationSettings.defaultProductGroupId];
                                }
                            }

                            return params;
                        }
                    }
                },
                productCode: {
                    type: 'string',
                    label: 'Product code',
                    column: {
                        populate: 'product',
                        pinned: 'left',
                        minWidth: 120
                    },
                    editor: {
                        collection: 'inventory.products',
                        view: 'inventory.catalog.products',
                        labelFrom: 'code',
                        valueFrom: 'code',
                        idFrom: 'productId',
                        extraFields: ['displayName'],
                        htmlTemplate(item) {
                            return item.displayName;
                        },
                        filters() {
                            const filters = {
                                isSimple: true,
                                canBePurchased: true
                            };

                            if (_.isObject(vm.organizationSettings) && !_.isEmpty(vm.organizationSettings)) {
                                filters.groupIds = {
                                    $in: vm.organizationSettings.productGroupIds || []
                                };
                            }

                            return filters;
                        },
                        updateParams(params, type) {
                            if (type === 'create' || type === 'detail') {
                                params.model = {
                                    isSimple: true,
                                    canBePurchased: true
                                };

                                if (
                                    _.isObject(vm.organizationSettings) &&
                                    !_.isEmpty(vm.organizationSettings) &&
                                    vm.organizationSettings.defaultProductGroupId
                                ) {
                                    params.model.groupIds = [vm.organizationSettings.defaultProductGroupId];
                                }
                            }

                            return params;
                        }
                    }
                },
                productDefinition: {
                    type: 'string',
                    label: 'Product definition',
                    column: {
                        populate: 'product',
                        // visible: false,
                        minWidth: 150
                    },
                    editor: {
                        collection: 'inventory.products',
                        view: 'inventory.catalog.products',
                        labelFrom: 'definition',
                        valueFrom: 'definition',
                        idFrom: 'productId',
                        extraFields: ['displayName'],
                        htmlTemplate(item) {
                            return item.displayName;
                        },
                        filters() {
                            const filters = {
                                isSimple: true,
                                canBePurchased: true
                            };

                            if (_.isObject(vm.organizationSettings) && !_.isEmpty(vm.organizationSettings)) {
                                filters.groupIds = {
                                    $in: vm.organizationSettings.productGroupIds || []
                                };
                            }

                            return filters;
                        },
                        updateParams(params, type) {
                            if (type === 'create' || type === 'detail') {
                                params.model = {
                                    isSimple: true,
                                    canBePurchased: true
                                };

                                if (
                                    _.isObject(vm.organizationSettings) &&
                                    !_.isEmpty(vm.organizationSettings) &&
                                    vm.organizationSettings.defaultProductGroupId
                                ) {
                                    params.model.groupIds = [vm.organizationSettings.defaultProductGroupId];
                                }
                            }

                            return params;
                        }
                    }
                },
                productCategoryId: {
                    type: 'string',
                    label: 'Product category',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                productCategoryPath: {
                    type: 'string',
                    label: 'Product category path',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                productType: {
                    type: 'string',
                    label: 'Product type',
                    required: false,
                    column: {
                        width: 150,
                        visible: false,
                        valueLabels: [
                            {value: 'stockable', label: 'Stockable product'},
                            {value: 'service', label: 'Service product'}
                        ],
                        translateLabels: true
                    },
                    editor: {
                        disabled: true
                    }
                },
                barcode: {
                    type: 'string',
                    label: 'Barcode',
                    required: false,
                    column: {
                        width: 150,
                        visible: false
                    }
                },
                contractId: {
                    type: 'string',
                    label: 'Contract',
                    required: false,
                    column: {
                        populate: 'contract',
                        visible: false,
                        width: 120
                    },
                    editor: {
                        collection: 'purchase.contracts',
                        view: 'purchase.contract.contracts',
                        filters() {
                            return {
                                status: 'approved',
                                partnerId: vm.model.partnerId,
                                startDate: {$lte: new Date()},
                                endDate: {$gte: new Date()}
                            };
                        },
                        disableCreate: true,
                        extraFields: ['code'],
                        template: '{{code}} - {{name}}'
                    }
                },
                supplierCatalogNo: {
                    type: 'string',
                    label: 'Supplier catalog no',
                    required: false,
                    column: {
                        width: 150
                    }
                },
                deliveryNote: {
                    type: 'string',
                    label: 'Delivery note',
                    required: false,
                    column: {
                        width: 200,
                        visible: true
                    },
                    editor: {}
                },
                description: {
                    type: 'string',
                    label: 'Description',
                    required: false,
                    column: {
                        visible: false
                    }
                },
                scheduledDate: {
                    type: 'date',
                    label: 'Delivery Date',
                    default: 'date:now',
                    column: {
                        visible: false,
                        width: 120
                    }
                },
                branchId: {
                    type: 'string',
                    label: 'Branch Office',
                    required: false,
                    column: {
                        populate: 'branch',
                        width: 180,
                        visible: false,
                        hidden: !vm.$setting('system.multiBranch')
                    },
                    editor: {
                        collection: 'kernel.branches',
                        disabled: true
                    }
                },
                warehouseId: {
                    type: 'string',
                    label: 'Warehouse',
                    required: false,
                    column: {
                        populate: 'warehouse',
                        width: 180,
                        visible: false
                    },
                    editor: {
                        collection: 'inventory.warehouses',
                        view: 'inventory.configuration.warehouses',
                        filters() {
                            const filters = {branchId: vm.model.branchId};

                            if (
                                _.isObject(vm.organizationSettings) &&
                                !_.isEmpty(vm.organizationSettings) &&
                                Array.isArray(vm.organizationSettings.warehouseIds) &&
                                vm.organizationSettings.warehouseIds.length > 0
                            ) {
                                filters._id = {
                                    $in: vm.organizationSettings.warehouseIds || []
                                };
                            }

                            return filters;
                        },
                        extraFields: ['shortName'],
                        template: '{{shortName}} - {{name}}'
                    }
                },
                quantity: {
                    type: 'decimal',
                    label: 'Quantity',
                    default: 1,
                    column: {
                        width: 75,
                        format: 'unit',
                        formatOptions(row) {
                            return {
                                number: {
                                    precision: vm.$setting('system.unitPrecision')
                                }
                            };
                        }
                    },
                    editor: {}
                },
                unitId: {
                    type: 'string',
                    label: 'Unit',
                    required: false,
                    column: {
                        populate: 'unit',
                        width: 60
                    },
                    editor: {
                        collection: 'kernel.units',
                        filters(data) {
                            let ids = [];

                            if (_.isObject(data.product)) {
                                if (_.isString(data.product.baseUnitId)) {
                                    ids.push(data.product.baseUnitId);
                                }

                                const unitConversions = data.product.unitConversions;
                                if (Array.isArray(unitConversions) && unitConversions.length > 0) {
                                    unitConversions.forEach(uc => {
                                        ids.push(uc.fromUnitId);
                                        ids.push(uc.toUnitId);
                                    });
                                }
                            }

                            return {_id: {$in: _.uniq(ids)}};
                        }
                    }
                },
                baseQuantity: {
                    type: 'decimal',
                    label: 'Base Quantity',
                    default: 1,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                baseUnitId: {
                    type: 'string',
                    label: 'Base unit',
                    required: false,
                    column: {
                        populate: 'baseUnit',
                        width: 60,
                        visible: false
                    },
                    editor: {
                        collection: 'kernel.units',
                        disabled: true
                    }
                },
                listPriceId: {
                    type: 'string',
                    label: 'List price',
                    required: false,
                    column: {
                        populate: 'listPrice',
                        width: 150,
                        visible: false,
                        hidden: !vm.$setting('purchase.purchaseListPrice')
                    },
                    editor: {
                        collection: 'purchase.list-prices',
                        view: 'purchase.pricing.list-prices',
                        filters(row) {
                            return vm.listPriceIdFilters;
                        },
                        disabled: () => {
                            return vm.status === 'payment-planned';
                        }
                    }
                },
                currencyId: {
                    type: 'string',
                    label: 'Currency',
                    required: false,
                    column: {
                        populate: 'currency',
                        width: 90,
                        visible: false
                    },
                    editor: {
                        collection: 'kernel.currencies',
                        disabled: () => {
                            return vm.status === 'payment-planned';
                        }
                    }
                },
                currencyRate: {
                    type: 'decimal',
                    label: 'Currency rate',
                    default: 1,
                    column: {
                        format: 'amount',
                        width: 95,
                        visible: false,
                        formatOptions(row) {
                            return {
                                number: {
                                    precision: vm.$setting('system.exchangeRatePrecision')
                                }
                            };
                        }
                    },
                    editor: {
                        precision: vm.$setting('system.exchangeRatePrecision')
                    }
                },
                unitPrice: {
                    type: 'decimal',
                    label: 'Unit Price',
                    default: 0,
                    column: {
                        format: 'unit-price',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        width: 95
                    },
                    editor: {}
                },
                unitPriceFC: {
                    type: 'decimal',
                    label: 'Unit Price (FC)',
                    default: 0,
                    column: {
                        format: 'unit-price',
                        formatOptions(row) {
                            if (_.isObject(row) && row.currencyId) {
                                const currency = currenciesMap[row.currencyId];

                                if (!!currency) {
                                    let options = {currency: {}};

                                    options.currency.symbol = currency.symbol;
                                    options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                                    return options;
                                }
                            }

                            return vm.currencyFormat;
                        },
                        visible: false,
                        width: 95
                    },
                    editor: {}
                },
                grossUnitPrice: {
                    type: 'decimal',
                    label: 'Gross Unit Price',
                    required: false,
                    column: {
                        format: 'unit-price',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        visible: false,
                        width: 95
                    },
                    editor: {}
                },
                discount: {
                    type: 'decimal',
                    label: 'Discount %',
                    default: 0,
                    column: {
                        format: 'percentage',
                        width: 80,
                        formatOptions(row) {
                            return {
                                number: {
                                    precision: vm.$setting('system.percentagePrecision')
                                }
                            };
                        },
                        hidden: !vm.$setting('purchase.lineDiscounts'),
                        cellRendererParams({data: row}) {
                            return {
                                handler: vm.handleApplyMultipleDiscounts,
                                disabled: vm.status !== 'draft',
                                hasDiscountPayload: !_.isEmpty(row.discountPayload)
                            };
                        },
                        cellRendererFramework: DiscountCellRenderer
                    },
                    editor: {
                        disabled: row => {
                            return !_.isEmpty(row.discountPayload);
                        }
                    }
                },
                discountPayload: {
                    type: 'object',
                    blackbox: true,
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                unitPriceAfterDiscount: {
                    type: 'decimal',
                    label: 'Unit Price After Discount',
                    default: 0,
                    column: {
                        format: 'unit-price',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        visible: false,
                        width: 95
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                grossUnitPriceAfterDiscount: {
                    type: 'decimal',
                    label: 'Gross Unit Price After Discount',
                    default: 0,
                    column: {
                        format: 'unit-price',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        visible: false,
                        width: 95
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                freight: {
                    type: 'decimal',
                    label: 'Freight',
                    default: 0,
                    column: {
                        format: 'unit-price',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        hidden: !vm.$setting('system.freight'),
                        width: 95
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                taxId: {
                    type: 'string',
                    label: 'Tax',
                    required: false,
                    column: {
                        populate: 'tax',
                        cellRendererParams({data: row}) {
                            return {
                                handler: vm.handleApplyMultipleTaxes,
                                disabled: !vm.model.partnerId || vm.status !== 'draft',
                                hasTaxPayload: !_.isEmpty(row.taxPayload)
                            };
                        },
                        cellRendererFramework: TaxCellRenderer,
                        width: 100
                    },
                    editor: {
                        collection: 'kernel.taxes',
                        filters(data) {
                            return {scope: 'purchase'};
                        },
                        disabled: row => {
                            return !_.isEmpty(row.taxPayload);
                        }
                    }
                },
                taxDetail: {
                    type: 'object',
                    blackbox: true,
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                taxPayload: {
                    type: 'object',
                    blackbox: true,
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                taxTotal: {
                    type: 'decimal',
                    label: 'Tax Total',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        visible: false,
                        width: 95
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                grossTotal: {
                    type: 'decimal',
                    label: 'Gross Total',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        visible: false,
                        width: 150
                    }
                },
                realTotal: {
                    type: 'decimal',
                    label: 'Real Total',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        visible: false,
                        width: 150
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                stockQuantity: {
                    type: 'decimal',
                    label: 'Stock on Hand',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                orderedQuantity: {
                    type: 'decimal',
                    label: 'Ordered Quantity',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                assignedQuantity: {
                    type: 'decimal',
                    label: 'Assigned Quantity',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                availableQuantity: {
                    type: 'decimal',
                    label: 'Available Quantity',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                warehouseStockQuantity: {
                    type: 'decimal',
                    label: 'Warehouse Stock on Hand',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                warehouseOrderedQuantity: {
                    type: 'decimal',
                    label: 'Warehouse Ordered Quantity',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                warehouseAssignedQuantity: {
                    type: 'decimal',
                    label: 'Warehouse Assigned Quantity',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                warehouseAvailableQuantity: {
                    type: 'decimal',
                    label: 'Warehouse Available Quantity',
                    default: 0,
                    column: {
                        width: 75,
                        format: 'unit',
                        visible: false
                    },
                    editor: {
                        disabled: () => true
                    }
                },
                financialProjectId: {
                    type: 'string',
                    label: 'Project',
                    required: false,
                    column: {
                        populate: 'financialProject',
                        visible: false,
                        width: 150
                    },
                    editor: {
                        collection: 'kernel.financial-projects',
                        view: 'system.management.configuration.financial-projects',
                        disableCreate: true,
                        disableDetail: true,
                        extraFields: ['code'],
                        template: '{{code}} - {{name}}',
                        filters: () => ({
                            $and: [
                                {
                                    $or: [
                                        {validFrom: {$exists: false}},
                                        {validFrom: {$eq: null}},
                                        {
                                            validFrom: {
                                                $lte: vm.model.quotationDate
                                            }
                                        }
                                    ]
                                },
                                {
                                    $or: [
                                        {validTo: {$exists: false}},
                                        {validTo: {$eq: null}},
                                        {
                                            validTo: {
                                                $gte: vm.model.quotationDate
                                            }
                                        }
                                    ]
                                }
                            ],
                            $sort: {code: 1}
                        })
                    }
                },
                total: {
                    type: 'decimal',
                    label: 'Total',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions() {
                            return vm.currencyFormat;
                        },
                        width: 150
                    }
                },
                totalFC: {
                    type: 'decimal',
                    label: 'Total (FC)',
                    default: 0,
                    column: {
                        format: 'currency',
                        formatOptions(row) {
                            if (_.isObject(row) && row.currencyId) {
                                const currency = currenciesMap[row.currencyId];

                                if (!!currency) {
                                    let options = {currency: {}};

                                    options.currency.symbol = currency.symbol;
                                    options.currency.format = currency.symbolPosition === 'before' ? '%s %v' : '%v %s';

                                    return options;
                                }
                            }

                            return vm.currencyFormat;
                        },
                        visible: false,
                        width: 150
                    },
                    editor: {
                        disabled: true
                    }
                },

                // Internal
                partnerId: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                invoiceResponsibleId: {
                    type: 'string',
                    label: 'Invoice responsible',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                invoiceAddressId: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                invoiceAddress: {
                    type: 'object',
                    label: 'Invoice address',
                    blackbox: true,
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                deliveryReceiverId: {
                    type: 'string',
                    label: 'Delivery receiver',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                deliveryAddressId: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                deliveryAddressCode: {
                    type: 'string',
                    label: 'Delivery address code',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                deliveryAddress: {
                    type: 'object',
                    label: 'Delivery address',
                    blackbox: true,
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                contractApplied: {
                    type: 'boolean',
                    default: false,
                    column: {
                        hidden: true
                    }
                },
                discountListApplied: {
                    type: 'boolean',
                    default: false,
                    column: {
                        hidden: true
                    }
                },
                brandId: {
                    type: 'string',
                    label: 'Brand',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                brandName: {
                    type: 'string',
                    label: 'Brand name',
                    required: false,
                    column: {
                        width: 150,
                        visible: false
                    }
                },
                shippingUnitId: {
                    type: 'string',
                    label: 'Shipping unit',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                typeOfGoodsId: {
                    type: 'string',
                    label: 'Type of goods',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                hsCode: {
                    type: 'string',
                    label: 'HS code',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                manufacturerId: {
                    type: 'string',
                    label: 'Manufacturer',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                manufacturerProductCode: {
                    type: 'string',
                    label: 'Manufacturer product code',
                    required: false,
                    column: {
                        width: 150,
                        visible: false
                    }
                },
                countryOfManufactureId: {
                    type: 'string',
                    label: 'Country of manufacture',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                classificationCode: {
                    type: 'string',
                    label: 'Classification code',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                classificationVersion: {
                    type: 'string',
                    label: 'Classification version',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                classificationValue: {
                    type: 'string',
                    label: 'Classification value',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                countryOfOriginId: {
                    type: 'string',
                    label: 'Country of origin',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                containerTypeId: {
                    type: 'string',
                    label: 'Container type',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                containerNo: {
                    type: 'string',
                    label: 'Container no',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                containerBrand: {
                    type: 'string',
                    label: 'Container brand',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                containerQuantity: {
                    type: 'decimal',
                    label: 'Container quantity',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                netWeight: {
                    type: 'decimal',
                    label: 'Net weight',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                netWeightUnitId: {
                    type: 'string',
                    label: 'Net weight unit',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                grossWeight: {
                    type: 'decimal',
                    label: 'Gross weight',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                grossWeightUnitId: {
                    type: 'string',
                    label: 'Gross weight unit',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                netVolume: {
                    type: 'decimal',
                    label: 'Net volume',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                netVolumeUnitId: {
                    type: 'string',
                    label: 'Net volume unit',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                grossVolume: {
                    type: 'decimal',
                    label: 'Gross volume',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                grossVolumeUnitId: {
                    type: 'string',
                    label: 'Gross volume unit',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                height: {
                    type: 'decimal',
                    label: 'Height',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                heightUnitId: {
                    type: 'string',
                    label: 'Height unit',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                width: {
                    type: 'decimal',
                    label: 'Width',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                widthUnitId: {
                    type: 'string',
                    label: 'Width unit',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                depth: {
                    type: 'decimal',
                    label: 'Depth',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                depthUnitId: {
                    type: 'string',
                    label: 'Depth unit',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                paymentResponsibleId: {
                    type: 'string',
                    label: 'Payment responsible',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                paymentAddressId: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                paymentAddress: {
                    type: 'object',
                    label: 'Payment address',
                    blackbox: true,
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                pcmModelId: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                pcmConfigurationId: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                },
                pcmHash: {
                    type: 'string',
                    required: false,
                    column: {
                        hidden: true
                    }
                }
            }
        ],

        // Organization
        organizationId: {
            type: 'string',
            label: 'Organization',
            required: false
        },
        purchaseManagerId: {
            type: 'string',
            label: 'Purchase manager',
            required: false
        },
        purchaseRepresentativeId: {
            type: 'string',
            label: 'Purchase representative',
            required: false
        },

        // Exchange rates.
        exchangeRates: {
            type: [
                {
                    currencyName: {
                        type: 'string',
                        label: 'Currency',
                        required: false,
                        column: {
                            width: 150
                        },
                        editor: {
                            disabled: true
                        }
                    },
                    rate: {
                        type: 'decimal',
                        label: 'Rate',
                        default: 0,
                        column: {
                            format: 'amount',
                            formatOptions() {
                                return {
                                    number: {
                                        precision: vm.$setting('system.exchangeRatePrecision')
                                    }
                                };
                            }
                        },
                        editor: {}
                    },
                    grandTotal: {
                        type: 'decimal',
                        label: 'Grand total',
                        default: 0,
                        column: {
                            format: 'amount',
                            valueGetter: params => {
                                const {data} = params;

                                if (!!data && _.isNumber(data.rate) && data.rate > 0) {
                                    return (vm.model.grandTotal * vm.model.currencyRate) / data.rate;
                                }

                                return 0;
                            }
                        },
                        editor: {
                            disabled: () => true
                        }
                    }
                }
            ],
            default: []
        },
        exchangeRatesMap: {
            type: 'object',
            blackbox: true,
            required: false
        },

        // Logistic
        warehouseId: {
            type: 'string',
            label: 'Warehouse'
        },
        invoiceResponsibleId: {
            type: 'string',
            label: 'Invoice responsible',
            required: false
        },
        invoiceAddressId: {
            type: 'string',
            required: false
        },
        invoiceAddress: {
            type: 'object',
            label: 'Invoice address',
            blackbox: true,
            required: false
        },
        deliveryReceiverId: {
            type: 'string',
            label: 'Delivery receiver',
            required: false
        },
        deliveryAddressId: {
            type: 'string',
            required: false
        },
        deliveryAddressCode: {
            type: 'string',
            label: 'Delivery address code',
            required: false
        },
        deliveryAddress: {
            type: 'object',
            label: 'Delivery address',
            blackbox: true,
            required: false
        },
        deliveryPriority: {
            type: 'string',
            label: 'Delivery priority',
            allowed: ['not-urgent', 'normal', 'urgent', 'very-urgent'],
            default: 'normal'
        },
        deliveryPolicy: {
            type: 'string',
            label: 'Delivery policy',
            allowed: ['when-one-ready', 'when-all-ready'],
            default: 'when-one-ready'
        },
        deliveryConditionId: {
            type: 'string',
            label: 'Delivery condition',
            required: false
        },
        deliveryMethodId: {
            type: 'string',
            label: 'Delivery method',
            required: false
        },
        deliveryNote: {
            type: 'string',
            label: 'Delivery note',
            required: false
        },
        imoAndMmsiNo: {
            type: 'string',
            label: 'IMO and MMSI no',
            required: false
        },
        shipName: {
            type: 'string',
            label: 'Ship name',
            required: false
        },
        shipRadioCallName: {
            type: 'string',
            label: 'Ship radio call name',
            required: false
        },
        shipRegistrationName: {
            type: 'string',
            label: 'Ship registration name',
            required: false
        },
        shipNetWeight: {
            type: 'decimal',
            label: 'Ship net weight',
            required: false
        },
        shipGrossWeight: {
            type: 'decimal',
            label: 'Ship gross weight',
            required: false
        },
        shipRequirements: {
            type: 'string',
            label: 'Ship requirements',
            required: false
        },
        shipPortOfRegistration: {
            type: 'string',
            label: 'Ship port of registration',
            required: false
        },
        trainNo: {
            type: 'string',
            label: 'Train no',
            required: false
        },
        trainWagonNo: {
            type: 'string',
            label: 'Train wagon no',
            required: false
        },
        licensePlateNo: {
            type: 'string',
            label: 'License plate no',
            required: false
        },
        aircraftNo: {
            type: 'string',
            label: 'Aircraft no',
            required: false
        },
        carrierId: {
            type: 'string',
            label: 'Carrier',
            required: false
        },
        cargoTrackingCode: {
            type: 'string',
            label: 'Cargo tracking code',
            required: false
        },
        shippingPaymentType: {
            type: 'string',
            label: 'Shipment payment type',
            required: false
        },

        // Financial.
        financialProjectId: {
            type: 'string',
            label: 'Project',
            required: false,
            index: true
        },
        paymentTermId: {
            type: 'string',
            label: 'Payment term'
        },
        listPriceId: {
            type: 'string',
            label: 'List price',
            required: false
        },
        guaranteeId: {
            type: 'string',
            label: 'Guarantee',
            required: false
        },
        paymentTerm: {
            type: 'object',
            blackbox: true,
            required: false
        },
        paymentPlan: {
            type: 'object',
            blackbox: true,
            required: false
        },
        paymentPlanningDate: {
            type: 'date',
            label: 'Payment planning date',
            required: false
        },
        paymentResponsibleId: {
            type: 'string',
            label: 'Payment responsible',
            required: false
        },
        paymentAddressId: {
            type: 'string',
            required: false
        },
        paymentAddress: {
            type: 'object',
            label: 'Payment address',
            blackbox: true,
            required: false
        },

        content: {
            type: 'string',
            label: 'Content',
            required: false
        },

        // Attachments.
        attachments: {
            type: ['string'],
            default: []
        },

        // Internal.
        paymentPlanBackup: {
            type: 'object',
            blackbox: true,
            required: false
        },
        contractParams: {
            type: 'object',
            blackbox: true,
            required: false
        },
        freight: {
            type: 'object',
            blackbox: true,
            required: false
        },
        // Additional information
        additionalInformation: {
            type: 'object',
            blackbox: true,
            required: false
        },
        additionalInformationId: {
            type: 'string',
            label: 'Additional information',
            required: false
        }
    };
}
