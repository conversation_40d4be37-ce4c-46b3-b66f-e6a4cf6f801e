<template>
    <div class="purchase-request-procurements-content" v-if="!!payload">
        <div class="procurements-content-top">
            <div class="content-top-title">
                <template v-if="payload.type === 'product'">{{ payload.displayName }}</template>
                <template v-else-if="payload.type === 'vendor'">{{ payload.code }} - {{ payload.name }}</template>
                <template v-else>{{ payload.code }}</template>
            </div>

            <div class="content-top-actions">
                <el-button
                    :loading="$params('loading')"
                    type="primary"
                    icon="far fa-sync-alt"
                    :disabled="selected.length < 1"
                    @click="handleConvert"
                >
                    {{ 'Convert' | t }}
                </el-button>

                <el-button
                    :loading="$params('loading')"
                    type="primary"
                    icon="far fa-truck"
                    :disabled="selected.length < 1"
                    @click="handleTransport"
                >
                    {{ 'Transport From Stock' | t }}
                </el-button>

                <el-button
                    :loading="$params('loading')"
                    plain
                    icon="far fa-ban"
                    :disabled="selected.length < 1"
                    @click="handleCancel"
                >
                    {{ 'Cancel' | t }}
                </el-button>
            </div>

            <div class="content-top-search">
                <el-input
                    v-model="search"
                    :placeholder="'Search..' | t"
                    prefix-icon="el-icon-search"
                    clearable
                    size="mini"
                />
            </div>
        </div>
        <div class="procurements-content-items">
            <ui-table
                id="purchase.request-procurements.items"
                :items="items"
                :schema="itemsSchema"
                :search="search"
                :min-empty-rows="0"
                :enable-add-remove="false"
                :enable-auto-height="false"
                row-selection="multiple"
                enable-editing
                :options="tableOptions"
                :before-init="beforeItemsInit"
                :before-create="beforeItemSave"
                :before-update="beforeItemSave"
                :after-update="handleItemChange"
                :summary-row="summaryRow"
                @selected="handleItemsSelect"
            />
        </div>
    </div>
</template>

<script>
import _ from 'lodash';
import ConversionOptions from './_conversion-options';
import StockTransport from './_stock-transport';
import itemsSchema from './items-schema';

export default {
    props: {
        query: Object,
        payload: Object
    },

    data: () => ({
        items: null,
        selected: [],
        search: '',
        tableOptions: {
            suppressRowClickSelection: true
        }
    }),

    computed: {
        itemsSchema() {
            return itemsSchema(this);
        }
    },

    methods: {
        handleConvert() {
            const self = this;
            const ids = [];

            for (const selected of this.selected) {
                if (!selected.vendorId) {
                    this.$program.message('error', this.$t('Vendor must be defined on all the selected rows!'));

                    return;
                }

                ids.push(selected._id);
            }

            this.$program.dialog({
                component: ConversionOptions,
                params: {
                    title: 'Convert'
                },
                async onSubmit(data) {
                    self.$params('loading', true);

                    try {
                        await self.$rpc('purchase.request-procurements-convert', {
                            conversionType: data.conversionType,
                            ids
                        });
                        await self.refreshItems();
                        self.selected = [];

                        self.$program.message('success', self.$t('Selected items are converted successfully.'));
                    } catch (error) {
                        self.$program.message('error', error.message);
                    }

                    self.$params('loading', false);
                }
            });
        },
        handleTransport() {
            const self = this;

            this.$program.dialog({
                component: StockTransport,
                params: {
                    title: 'Transport From Stock',
                    selected: this.selected
                },
                async onSubmit({items}) {
                    self.$params('loading', true);

                    try {
                        await self.$rpc('purchase.request-procurements-transport', {items});
                        await self.refreshItems();
                        self.selected = [];

                        self.$program.message('success', self.$t('Selected items are transported successfully.'));
                    } catch (error) {
                        self.$program.message('error', error.message);
                    }

                    self.$params('loading', false);
                }
            });
        },
        async handleCancel() {
            const isConfirmed = await new Promise(resolve => {
                this.$program.alert(
                    'confirm',
                    this.$t('You are about to cancel the document. Do you want continue?'),
                    confirmed => {
                        resolve(confirmed);
                    }
                );
            });

            if (!isConfirmed) return;

            const rpIds = this.selected.map(s => s._id);

            this.$program.dialog({
                component: 'purchase.procurement-and-source.request-procurements.canceling-reason',
                params: {
                    rpIds,
                    title: this.$t('Canceling Reason'),
                    isPreview: false
                },
                onSubmit: async () => {
                    this.$params('loading', true);

                    try {
                        await this.$rpc('purchase.request-procurements-cancel', {ids: rpIds});
                        await this.refreshItems();
                        this.selected = [];

                        this.$program.message('success', this.$t('Selected items are canceled successfully.'));
                    } catch (error) {
                        this.$program.message('error', error.message);
                    }

                    this.$params('loading', false);
                }
            });
        },
        handleItemsSelect(selected) {
            this.selected = selected;
        },
        async handleItemChange({row}) {
            await this.$rpc('purchase.request-procurements-update-item', {
                _id: row._id,
                vendorId: row.vendorId,
                approvedQuantity: row.approvedQuantity,
                openQuantity: row.openQuantity
            });
        },
        async beforeItemsInit(items) {
            let vendorIds = [];
            let vendors = [];
            let requestIds = [];
            let requests = [];
            items.forEach(item => {
                if (item.vendorId) vendorIds.push(item.vendorId);
                if (item.relatedPartnerId) vendorIds.push(item.relatedPartnerId);
                if (item.requestId) requestIds.push(item.requestId);
            });
            vendorIds = _.uniq(vendorIds);
            requestIds = _.uniq(requestIds);
            if (vendorIds.length > 0) {
                vendors = await this.$collection('kernel.partners').find({
                    _id: {$in: vendorIds},
                    $select: ['code', 'name'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
            }
            if (requestIds.length > 0) {
                requests = await this.$collection('purchase.requests').find({
                    _id: {$in: requestIds},
                    $select: ['_id', 'code'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
            }

            return items.map(item => {
                if (item.vendorId && vendors.length > 0) {
                    item.vendor = vendors.find(vendor => vendor._id === item.vendorId);
                }

                if (item.relatedPartnerId && vendors.length > 0) {
                    item.relatedPartner = vendors.find(p => p._id === item.relatedPartnerId);
                }

                if (item.requestId && requests.length > 0) {
                    item.request = requests.find(request => request._id === item.requestId);
                }

                return item;
            });
        },
        async beforeItemSave({row, params}) {
            const field = params.colDef.field;

            if (field === 'vendorId' && !!row.vendorId) {
                row.vendor = await this.$collection('kernel.partners').findOne({
                    _id: row.vendorId,
                    $select: ['code', 'name'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
            }
            if (field === 'relatedPartnerId' && !!row.relatedPartnerId) {
                row.relatedPartner = await this.$collection('kernel.partners').findOne({
                    _id: row.relatedPartnerId,
                    $select: ['code', 'name'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
            }

            if (!!row.requestId) {
                row.request = await this.$collection('purchase.requests').findOne({
                    _id: row.requestId,
                    $select: ['_id', 'code'],
                    $disableActiveCheck: true,
                    $disableSoftDelete: true
                });
            }

            row.openQuantity = row.quantity - row.approvedQuantity - row.completedQuantity;
            if (row.openQuantity < 0) {
                row.openQuantity = 0;
            }

            return row;
        },
        summaryRow(items) {
            const row = {
                rowCount: 0,
                quantity: 0,
                approvedQuantity: 0,
                completedQuantity: 0,
                openQuantity: 0
            };

            for (const item of items) {
                row.rowCount++;
                row.quantity = item.quantity;
                row.approvedQuantity = item.approvedQuantity || 0;
                row.openQuantity = item.openQuantity || 0;
                row.completedQuantity = item.completedQuantity || 0;
            }

            row.quantity = this.$app.round(row.quantity, 'unit');
            row.approvedQuantity = this.$app.round(row.approvedQuantity, 'unit');
            row.openQuantity = this.$app.round(row.openQuantity, 'unit');
            row.completedQuantity = this.$app.round(row.completedQuantity, 'unit');

            return row;
        },
        async initializeItems() {
            this.$params('loading', true);

            const query = _.cloneDeep(this.query) || {};

            if (this.payload.type === 'product') {
                query.productId = this.payload._id;
            } else if (this.payload.type === 'vendor') {
                query.vendorId = this.payload._id;
            } else if (this.payload.type === 'request') {
                query.requestId = this.payload._id;
            }

            this.items = await this.$rpc('purchase.request-procurements-get-items', {query});

            this.$nextTick(() => {
                this.$params('loading', false);
            });
        },
        async refreshItems() {
            const query = _.cloneDeep(this.query) || {};

            if (this.payload.type === 'product') {
                query.productId = this.payload._id;
            } else if (this.payload.type === 'vendor') {
                query.vendorId = this.payload._id;
            } else if (this.payload.type === 'request') {
                query.requestId = this.payload._id;
            }

            this.items = await this.$rpc('purchase.request-procurements-get-items', {query});
        }
    },

    created() {
        this.initializeItems();
    }
};
</script>

<style lang="scss">
//noinspection CssUnknownTarget
@import 'core';

.purchase-request-procurements-content {
    position: relative;
    flex: 1 1 0;
    height: 100%;
    padding-top: 40px;
    overflow: hidden;

    .procurements-content-top {
        position: absolute;
        display: flex;
        flex-flow: row nowrap;
        top: 0;
        left: 0;
        width: 100%;
        height: 40px;
        padding: 7px;

        .content-top-title {
            flex: 1 1 0;
            line-height: 26px;
            color: $primary;
            font-size: 15px;
            @include text-truncate();
        }

        .content-top-search {
            flex: 0 0 180px;
            margin-left: 7px;
        }
    }

    .procurements-content-items {
        position: relative;
        width: 100%;
        height: 100%;
        overflow: hidden;

        .ui-table.is-editable {
            .ag-floating-bottom-viewport .ag-floating-bottom-container,
            .ag-pinned-left-floating-bottom,
            .ag-pinned-right-floating-bottom {
                .ag-row {
                    background-color: $primary !important;

                    .ag-cell {
                        color: #fff !important;
                        background-color: $primary !important;
                        border-bottom: 1px solid $primary !important;
                        height: 28px !important;
                        line-height: 28px !important;
                    }
                }
            }
        }
    }
}
</style>
