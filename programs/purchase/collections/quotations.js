import _ from 'lodash';

export default {
    name: 'quotations',
    title: 'Quotations',
    branch: true,
    assignable: true,
    view: 'purchase.purchase.quotations',
    labelParams: {
        from: 'code'
    },
    extraIndexes: [{type: 'normal', key: 'additionalInformation.*', value: 1}],
    schema: {
        // General
        status: {
            type: 'string',
            label: 'Status',
            allowed: ['draft', 'payment-planned', 'converted-to-order', 'converted-to-invoice', 'canceled'],
            default: 'draft',
            index: true
        },
        documentTypeId: {
            type: 'string',
            label: 'Document type',
            index: true
        },
        code: {
            type: 'string',
            label: 'Code',
            unique: true,
            index: true
        },
        reference: {
            type: 'string',
            label: 'Reference',
            index: true,
            required: false
        },
        partnerGroupId: {
            type: 'string',
            label: 'Vendor group',
            required: false,
            index: true
        },
        partnerId: {
            type: 'string',
            label: 'Vendor',
            index: true
        },
        currencyId: {
            type: 'string',
            label: 'Currency',
            index: true
        },
        currencyRate: {
            type: 'decimal',
            label: 'Currency rate',
            default: 1,
            index: true
        },
        contactPersonId: {
            type: 'string',
            label: 'Contact person',
            required: false
        },
        recordDate: {
            type: 'date',
            label: 'Record date',
            default: 'date:now',
            index: true
        },
        quotationDate: {
            type: 'date',
            label: 'Quotation date',
            default: 'date:now',
            index: true
        },
        expiryDate: {
            type: 'date',
            label: 'Expiry date',
            default: 'date:now',
            index: true
        },
        scheduledDate: {
            type: 'date',
            label: 'Delivery date',
            default: 'date:now',
            index: true
        },
        note: {
            type: 'string',
            label: 'Note',
            required: false
        },

        // Totals
        subTotal: {
            type: 'decimal',
            label: 'Subtotal',
            default: 0,
            index: true
        },
        discount: {
            type: 'decimal',
            label: 'Discount %',
            default: 0,
            index: true
        },
        discountAmount: {
            type: 'decimal',
            label: 'Discount',
            default: 0,
            index: true
        },
        subTotalAfterDiscount: {
            type: 'decimal',
            label: 'Subtotal after discount',
            default: 0,
            index: true
        },
        taxTotal: {
            type: 'decimal',
            label: 'Tax total',
            default: 0,
            index: true
        },
        rounding: {
            type: 'decimal',
            label: 'Rounding',
            default: 0,
            index: true
        },
        grandTotal: {
            type: 'decimal',
            label: 'Grand total',
            default: 0,
            index: true
        },
        appliedTaxes: {
            type: [
                {
                    type: 'object',
                    blackbox: true,
                    required: false
                }
            ],
            default: []
        },

        // Items
        items: {
            type: [
                {
                    id: {
                        type: 'string',
                        required: false
                    },
                    productId: {
                        type: 'string',
                        label: 'Product',
                        index: true
                    },
                    productCode: {
                        type: 'string',
                        label: 'Product code'
                    },
                    productDefinition: {
                        type: 'string',
                        label: 'Product definition'
                    },
                    productCategoryId: {
                        type: 'string',
                        label: 'Product category',
                        required: false
                    },
                    productCategoryPath: {
                        type: 'string',
                        label: 'Product category path',
                        required: false
                    },
                    productType: {
                        type: 'string',
                        label: 'Product type',
                        index: true
                    },
                    barcode: {
                        type: 'string',
                        label: 'Barcode',
                        required: false,
                        index: true
                    },
                    contractId: {
                        type: 'string',
                        label: 'Contract',
                        required: false,
                        index: true
                    },
                    supplierCatalogNo: {
                        type: 'string',
                        label: 'Supplier catalog no',
                        required: false
                    },
                    deliveryNote: {
                        type: 'string',
                        label: 'Delivery note',
                        required: false
                    },
                    description: {
                        type: 'string',
                        label: 'Description',
                        required: false
                    },
                    scheduledDate: {
                        type: 'date',
                        label: 'Delivery Date',
                        default: 'date:now'
                    },
                    branchId: {
                        type: 'string',
                        label: 'Branch Office'
                    },
                    warehouseId: {
                        type: 'string',
                        label: 'Warehouse'
                    },
                    quantity: {
                        type: 'decimal',
                        label: 'Quantity',
                        default: 1
                    },
                    unitId: {
                        type: 'string',
                        label: 'Unit'
                    },
                    baseUnitId: {
                        type: 'string',
                        label: 'Base unit'
                    },
                    baseQuantity: {
                        type: 'decimal',
                        label: 'Base quantity',
                        default: 1
                    },
                    listPriceId: {
                        type: 'string',
                        label: 'List price',
                        required: false
                    },
                    currencyId: {
                        type: 'string',
                        label: 'Currency',
                        required: false
                    },
                    currencyRate: {
                        type: 'decimal',
                        label: 'Currency rate',
                        default: 1
                    },
                    unitPrice: {
                        type: 'decimal',
                        label: 'Unit Price',
                        default: 0
                    },
                    unitPriceFC: {
                        type: 'decimal',
                        label: 'Unit price (FC)',
                        default: 0
                    },
                    grossUnitPrice: {
                        type: 'decimal',
                        label: 'Gross Unit Price',
                        required: false
                    },
                    discount: {
                        type: 'decimal',
                        label: 'Discount %',
                        default: 0
                    },
                    discountPayload: {
                        type: 'object',
                        blackbox: true,
                        required: false,
                        column: {
                            hidden: true
                        }
                    },
                    unitPriceAfterDiscount: {
                        type: 'decimal',
                        label: 'Unit price after discount',
                        default: 0
                    },
                    grossUnitPriceAfterDiscount: {
                        type: 'decimal',
                        label: 'Gross unit price after discount',
                        default: 0
                    },
                    freight: {
                        type: 'decimal',
                        label: 'Freight',
                        default: 0
                    },
                    taxId: {
                        type: 'string',
                        label: 'Tax',
                        required: false
                    },
                    taxPayload: {
                        type: 'object',
                        blackbox: true,
                        required: false
                    },
                    taxDetail: {
                        type: 'object',
                        blackbox: true,
                        required: false
                    },
                    taxTotal: {
                        type: 'decimal',
                        label: 'Tax Total',
                        default: 0
                    },
                    grossTotal: {
                        type: 'decimal',
                        label: 'Gross total',
                        default: 0
                    },
                    realTotal: {
                        type: 'decimal',
                        label: 'Real Total',
                        default: 0,
                        index: true
                    },
                    stockQuantity: {
                        type: 'decimal',
                        label: 'Stock on hand',
                        default: 0
                    },
                    orderedQuantity: {
                        type: 'decimal',
                        label: 'Ordered quantity',
                        default: 0
                    },
                    assignedQuantity: {
                        type: 'decimal',
                        label: 'Assigned quantity',
                        default: 0
                    },
                    availableQuantity: {
                        type: 'decimal',
                        label: 'Available quantity',
                        default: 0
                    },
                    warehouseStockQuantity: {
                        type: 'decimal',
                        label: 'Warehouse stock on hand',
                        default: 0
                    },
                    warehouseOrderedQuantity: {
                        type: 'decimal',
                        label: 'Warehouse ordered quantity',
                        default: 0
                    },
                    warehouseAssignedQuantity: {
                        type: 'decimal',
                        label: 'Warehouse assigned quantity',
                        default: 0
                    },
                    warehouseAvailableQuantity: {
                        type: 'decimal',
                        label: 'Warehouse available quantity',
                        default: 0
                    },
                    financialProjectId: {
                        type: 'string',
                        label: 'Project',
                        required: false
                    },
                    total: {
                        type: 'decimal',
                        label: 'Total',
                        default: 0,
                        index: true
                    },
                    totalFC: {
                        type: 'decimal',
                        label: 'Total (FC)',
                        default: 0
                    },

                    // Internal
                    partnerId: {
                        type: 'string',
                        required: false
                    },
                    invoiceResponsibleId: {
                        type: 'string',
                        label: 'Invoice responsible',
                        required: false
                    },
                    invoiceAddressId: {
                        type: 'string',
                        required: false
                    },
                    invoiceAddress: {
                        type: 'object',
                        label: 'Invoice address',
                        blackbox: true,
                        required: false
                    },
                    deliveryReceiverId: {
                        type: 'string',
                        label: 'Delivery receiver',
                        required: false
                    },
                    deliveryAddressId: {
                        type: 'string',
                        required: false
                    },
                    deliveryAddressCode: {
                        type: 'string',
                        label: 'Delivery address code',
                        required: false
                    },
                    deliveryAddress: {
                        type: 'object',
                        label: 'Delivery address',
                        blackbox: true,
                        required: false
                    },
                    contractApplied: {
                        type: 'boolean',
                        default: false
                    },
                    discountListApplied: {
                        type: 'boolean',
                        default: false
                    },
                    brandId: {
                        type: 'string',
                        label: 'Brand',
                        required: false
                    },
                    brandName: {
                        type: 'string',
                        label: 'Brand name',
                        required: false
                    },
                    shippingUnitId: {
                        type: 'string',
                        label: 'Shipping unit',
                        required: false
                    },
                    typeOfGoodsId: {
                        type: 'string',
                        label: 'Type of goods',
                        required: false
                    },
                    hsCode: {
                        type: 'string',
                        label: 'HS code',
                        required: false
                    },
                    manufacturerId: {
                        type: 'string',
                        label: 'Manufacturer',
                        required: false
                    },
                    manufacturerProductCode: {
                        type: 'string',
                        label: 'Manufacturer product code',
                        required: false
                    },
                    countryOfManufactureId: {
                        type: 'string',
                        label: 'Country of manufacture',
                        required: false
                    },
                    classificationCode: {
                        type: 'string',
                        label: 'Classification code',
                        required: false
                    },
                    classificationVersion: {
                        type: 'string',
                        label: 'Classification version',
                        required: false
                    },
                    classificationValue: {
                        type: 'string',
                        label: 'Classification value',
                        required: false
                    },
                    countryOfOriginId: {
                        type: 'string',
                        label: 'Country of origin',
                        required: false
                    },
                    containerTypeId: {
                        type: 'string',
                        label: 'Container type',
                        required: false
                    },
                    containerNo: {
                        type: 'string',
                        label: 'Container no',
                        required: false
                    },
                    containerBrand: {
                        type: 'string',
                        label: 'Container brand',
                        required: false
                    },
                    containerQuantity: {
                        type: 'decimal',
                        label: 'Container quantity',
                        required: false
                    },
                    netWeight: {
                        type: 'decimal',
                        label: 'Net weight',
                        required: false
                    },
                    netWeightUnitId: {
                        type: 'string',
                        label: 'Net weight unit',
                        required: false
                    },
                    grossWeight: {
                        type: 'decimal',
                        label: 'Gross weight',
                        required: false
                    },
                    grossWeightUnitId: {
                        type: 'string',
                        label: 'Gross weight unit',
                        required: false
                    },
                    netVolume: {
                        type: 'decimal',
                        label: 'Net volume',
                        required: false
                    },
                    netVolumeUnitId: {
                        type: 'string',
                        label: 'Net volume unit',
                        required: false
                    },
                    grossVolume: {
                        type: 'decimal',
                        label: 'Gross volume',
                        required: false
                    },
                    grossVolumeUnitId: {
                        type: 'string',
                        label: 'Gross volume unit',
                        required: false
                    },
                    height: {
                        type: 'decimal',
                        label: 'Height',
                        required: false
                    },
                    heightUnitId: {
                        type: 'string',
                        label: 'Height unit',
                        required: false
                    },
                    width: {
                        type: 'decimal',
                        label: 'Width',
                        required: false
                    },
                    widthUnitId: {
                        type: 'string',
                        label: 'Width unit',
                        required: false
                    },
                    depth: {
                        type: 'decimal',
                        label: 'Depth',
                        required: false
                    },
                    depthUnitId: {
                        type: 'string',
                        label: 'Depth unit',
                        required: false
                    },
                    paymentResponsibleId: {
                        type: 'string',
                        label: 'Payment responsible',
                        required: false
                    },
                    paymentAddressId: {
                        type: 'string',
                        required: false
                    },
                    paymentAddress: {
                        type: 'object',
                        label: 'Payment address',
                        blackbox: true,
                        required: false
                    },
                    pcmModelId: {
                        type: 'string',
                        required: false
                    },
                    pcmConfigurationId: {
                        type: 'string',
                        required: false
                    },
                    pcmHash: {
                        type: 'string',
                        required: false
                    }
                }
            ],
            default: []
        },

        // Organization
        organizationId: {
            type: 'string',
            label: 'Organization',
            required: false
        },
        purchaseManagerId: {
            type: 'string',
            label: 'Purchase manager',
            required: false
        },
        purchaseRepresentativeId: {
            type: 'string',
            label: 'Purchase representative',
            required: false
        },

        // Exchange rates.
        exchangeRates: {
            type: [
                {
                    currencyName: {
                        type: 'string',
                        label: 'Currency'
                    },
                    rate: {
                        type: 'decimal',
                        label: 'Rate',
                        default: 0
                    }
                }
            ],
            default: []
        },
        exchangeRatesMap: {
            type: 'object',
            blackbox: true,
            required: false
        },

        // Logistic
        warehouseId: {
            type: 'string',
            label: 'Warehouse'
        },
        deliveryReceiverId: {
            type: 'string',
            label: 'Delivery receiver',
            required: false
        },
        deliveryAddressId: {
            type: 'string',
            required: false
        },
        deliveryAddressCode: {
            type: 'string',
            label: 'Delivery address code',
            required: false
        },
        deliveryAddress: {
            type: 'object',
            label: 'Delivery address',
            blackbox: true,
            required: false
        },
        invoiceResponsibleId: {
            type: 'string',
            label: 'Invoice responsible',
            required: false
        },
        invoiceAddressId: {
            type: 'string',
            required: false
        },
        invoiceAddress: {
            type: 'object',
            label: 'Invoice address',
            blackbox: true,
            required: false
        },
        deliveryPriority: {
            type: 'string',
            label: 'Delivery priority',
            allowed: ['not-urgent', 'normal', 'urgent', 'very-urgent'],
            default: 'normal'
        },
        deliveryPolicy: {
            type: 'string',
            label: 'Delivery policy',
            allowed: ['when-one-ready', 'when-all-ready'],
            default: 'when-one-ready'
        },
        deliveryConditionId: {
            type: 'string',
            label: 'Delivery condition',
            required: false
        },
        deliveryMethodId: {
            type: 'string',
            label: 'Delivery method',
            required: false
        },
        deliveryNote: {
            type: 'string',
            label: 'Delivery note',
            required: false
        },
        imoAndMmsiNo: {
            type: 'string',
            label: 'IMO and MMSI no',
            required: false
        },
        shipName: {
            type: 'string',
            label: 'Ship name',
            required: false
        },
        shipRadioCallName: {
            type: 'string',
            label: 'Ship radio call name',
            required: false
        },
        shipRegistrationName: {
            type: 'string',
            label: 'Ship registration name',
            required: false
        },
        shipNetWeight: {
            type: 'decimal',
            label: 'Ship net weight',
            required: false
        },
        shipGrossWeight: {
            type: 'decimal',
            label: 'Ship gross weight',
            required: false
        },
        shipRequirements: {
            type: 'string',
            label: 'Ship requirements',
            required: false
        },
        shipPortOfRegistration: {
            type: 'string',
            label: 'Ship port of registration',
            required: false
        },
        trainNo: {
            type: 'string',
            label: 'Train no',
            required: false
        },
        trainWagonNo: {
            type: 'string',
            label: 'Train wagon no',
            required: false
        },
        licensePlateNo: {
            type: 'string',
            label: 'License plate no',
            required: false
        },
        aircraftNo: {
            type: 'string',
            label: 'Aircraft no',
            required: false
        },
        carrierId: {
            type: 'string',
            label: 'Carrier',
            required: false
        },
        cargoTrackingCode: {
            type: 'string',
            label: 'Cargo tracking code',
            required: false
        },
        shippingPaymentType: {
            type: 'string',
            label: 'Shipment payment type',
            required: false
        },

        // Financial.
        financialProjectId: {
            type: 'string',
            label: 'Project',
            required: false,
            index: true
        },
        paymentTermId: {
            type: 'string',
            label: 'Payment term'
        },
        listPriceId: {
            type: 'string',
            label: 'List price',
            required: false
        },
        guaranteeId: {
            type: 'string',
            label: 'Guarantee',
            required: false
        },
        paymentTerm: {
            type: 'object',
            blackbox: true,
            required: false
        },
        paymentPlan: {
            type: 'object',
            blackbox: true,
            required: false
        },
        paymentPlanningDate: {
            type: 'date',
            label: 'Payment planning date',
            required: false
        },
        paymentResponsibleId: {
            type: 'string',
            label: 'Payment responsible',
            required: false
        },
        paymentAddressId: {
            type: 'string',
            required: false
        },
        paymentAddress: {
            type: 'object',
            label: 'Payment address',
            blackbox: true,
            required: false
        },

        content: {
            type: 'string',
            label: 'Content',
            required: false
        },

        // Attachments.
        attachments: {
            type: ['string'],
            default: []
        },

        // Internal.
        workflowApprovalStatus: {
            type: 'string',
            label: 'Workflow approval status',
            required: false,
            index: true
        },
        relatedDocuments: {
            type: [
                {
                    collection: 'string',
                    view: 'string',
                    title: 'string',
                    ids: {
                        type: ['string'],
                        default: []
                    }
                }
            ],
            default: []
        },
        paymentPlanBackup: {
            type: 'object',
            blackbox: true,
            required: false
        },
        contractParams: {
            type: 'object',
            blackbox: true,
            required: false
        },
        freight: {
            type: 'object',
            blackbox: true,
            required: false
        },
        // Additional information
        additionalInformation: {
            type: 'object',
            blackbox: true,
            required: false
        },
        additionalInformationId: {
            type: 'string',
            label: 'Additional information',
            required: false
        }
    },
    attributes: {
        documentType: {
            collection: 'purchase.document-types',
            parentField: 'documentTypeId',
            childField: '_id'
        },
        partnerGroup: {
            collection: 'kernel.partner-groups',
            parentField: 'partnerGroupId',
            childField: '_id'
        },
        partner: {
            collection: 'kernel.partners',
            parentField: 'partnerId',
            childField: '_id'
        },
        contactPerson: {
            collection: 'kernel.contacts',
            parentField: 'contactPersonId',
            childField: '_id'
        },
        branch: {
            collection: 'kernel.branches',
            parentField: 'branchId',
            childField: '_id'
        },
        warehouse: {
            collection: 'inventory.warehouses',
            parentField: 'warehouseId',
            childField: '_id'
        },
        currency: {
            collection: 'kernel.currencies',
            parentField: 'currencyId',
            childField: '_id'
        },
        organization: {
            collection: 'kernel.organizations',
            parentField: 'organizationId',
            childField: '_id'
        },
        purchaseManager: {
            collection: 'kernel.partners',
            parentField: 'purchaseManagerId',
            childField: '_id'
        },
        purchaseRepresentative: {
            collection: 'kernel.partners',
            parentField: 'purchaseRepresentativeId',
            childField: '_id'
        },
        listPrice: {
            collection: 'purchase.list-prices',
            parentField: 'listPriceId',
            childField: '_id'
        },
        guarantee: {
            collection: 'finance.guarantees',
            parentField: 'guaranteeId',
            childField: '_id'
        },
        financialProject: {
            collection: 'kernel.financial-projects',
            parentField: 'financialProjectId',
            childField: '_id'
        }
    },
    async searchTerms(document) {
        const values = Object.values(_.pick(document, ['code', 'reference']));

        if (_.isPlainObject(document.partner)) {
            values.push(document.partner.code);
            values.push(document.partner.name);
        }

        return values;
    },
    async copy(document) {
        const app = this.app;
        // const company = await app.collection('kernel.company').findOne({});
        const numbering = await app.collection('kernel.numbering').findOne({
            code: 'purchaseQuotationNumbering',
            $select: ['_id'],
            $disableInUseCheck: true,
            $disableActiveCheck: true,
            $disableSoftDelete: true
        });

        document.status = 'draft';
        document.recordDate = app.datetime.local().toJSDate();
        document.quotationDate = app.datetime.local().toJSDate();
        document.dueDate = app.datetime.local().toJSDate();
        document.expiryDate = app.datetime.local().plus({
            days: app.setting('purchase.defaultQuotationValidityDayCount')
        });
        document.isExpired = false;
        document.relatedDocuments = [];
        document.code = await app.rpc('kernel.common.request-number', {numberingId: numbering._id, save: true});

        // const currencies = await app.collection('kernel.currencies').find({
        //     $select: ['_id', 'name']
        // });
        // const exchangeRates = [];
        // const payloads = [];
        // for (const currency of currencies) {
        //     if (currency.name === company.currency.name) {
        //         continue;
        //     }

        //     payloads.push({
        //         from: currency.name,
        //         to: company.currency.name,
        //         value: 1,
        //         options: {
        //             date: document.issueDate
        //         }
        //     });
        // }
        // for (const payload of await app.rpc('kernel.common.convert-currencies', payloads)) {
        //     exchangeRates.push({
        //         currencyName: payload.from,
        //         rate: payload.rate
        //     });
        // }
        // document.exchangeRates = exchangeRates;
        // if (document.currencyId !== company.currencyId) {
        //     const documentCurrency = currencies.find(currency => currency._id === document.currencyId);

        //     if (!documentCurrency) {
        //         const exchangeRate = exchangeRates.find(
        //             exchangeRate => exchangeRate.currencyName === documentCurrency.name
        //         );

        //         if (!!exchangeRate) {
        //             document.currencyRate = exchangeRate.rate;
        //         }
        //     }
        // }

        for (const item of document.items || []) {
            if (!item.productId) continue;

            const stockQuery = {
                date: document.scheduledDate || app.datetime.local().toJSDate(),
                productId: item.productId
            };
            if (app.hasModule('pcm') && !!item.pcmHash) {
                stockQuery.pcmHash = item.pcmHash;
            }
            const report = await app.rpc('inventory.get-stock-report', stockQuery);

            if (Array.isArray(report) && report.length > 0) {
                const r = report[0];

                item.stockQuantity = r.stockQuantity;
                item.orderedQuantity = r.orderedQuantity;
                item.assignedQuantity = r.assignedQuantity;
                item.availableQuantity = r.availableQuantity;
            } else {
                item.stockQuantity = 0;
                item.orderedQuantity = 0;
                item.assignedQuantity = 0;
                item.availableQuantity = 0;
            }

            if (item.warehouseId) {
                const stockQuery = {
                    date: document.scheduledDate || app.datetime.local().toJSDate(),
                    productId: item.productId,
                    warehouseId: item.warehouseId
                };
                if (app.hasModule('pcm') && !!item.pcmHash) {
                    stockQuery.pcmHash = item.pcmHash;
                }
                const report = await app.rpc('inventory.get-stock-report', stockQuery);

                if (Array.isArray(report) && report.length > 0) {
                    const r = report[0];

                    item.warehouseStockQuantity = r.stockQuantity;
                    item.warehouseOrderedQuantity = r.orderedQuantity;
                    item.warehouseAssignedQuantity = r.assignedQuantity;
                    item.warehouseAvailableQuantity = r.availableQuantity;
                } else {
                    item.warehouseStockQuantity = 0;
                    item.warehouseOrderedQuantity = 0;
                    item.warehouseAssignedQuantity = 0;
                    item.warehouseAvailableQuantity = 0;
                }
            }
        }

        return document;
    },
    hooks: {
        after: {
            create: [syncCashFlowRecords],
            update: [syncCashFlowRecords],
            patch: [syncCashFlowRecords],
            remove: [syncCashFlowRecords]
        }
    }
};

async function syncCashFlowRecords(context) {
    // noinspection ES6MissingAwait
    (async () => {
        for (const result of Array.isArray(context.result) ? context.result : [context.result]) {
            await context.app.rpc('finance.cash-flow-sync-records', {
                collection: 'purchase.quotations',
                documentId: result._id
            });
        }
    })();

    return context;
}
