export default {
    name: 'migrate-add-delivery-note-to-quotations',
    async action(payload, params) {
        const app = this.app;
        const quotationsCollection = app.collection('purchase.quotations');
        
        console.log('Starting migration: Adding deliveryNote field to purchase quotation items...');
        
        try {
            // Find all quotations that have items without deliveryNote field
            const quotations = await quotationsCollection.find({
                'items.0': { $exists: true }, // Has at least one item
                $or: [
                    { 'items.deliveryNote': { $exists: false } }, // deliveryNote field doesn't exist
                    { 'items': { $elemMatch: { deliveryNote: { $exists: false } } } } // At least one item missing deliveryNote
                ]
            });
            
            console.log(`Found ${quotations.length} purchase quotations to update`);
            
            let updatedCount = 0;
            
            for (const quotation of quotations) {
                let needsUpdate = false;
                const updatedItems = quotation.items.map(item => {
                    if (!item.hasOwnProperty('deliveryNote')) {
                        needsUpdate = true;
                        return {
                            ...item,
                            deliveryNote: '' // Add empty deliveryNote field
                        };
                    }
                    return item;
                });
                
                if (needsUpdate) {
                    await quotationsCollection.patch(
                        { _id: quotation._id },
                        { items: updatedItems },
                        { user: params.user }
                    );
                    updatedCount++;
                    console.log(`Updated purchase quotation: ${quotation.code}`);
                }
            }
            
            console.log(`Migration completed. Updated ${updatedCount} purchase quotations.`);
            
            return {
                success: true,
                message: `Successfully added deliveryNote field to ${updatedCount} purchase quotations`,
                updatedCount
            };
            
        } catch (error) {
            console.error('Migration failed:', error);
            return {
                success: false,
                message: `Migration failed: ${error.message}`,
                error: error.message
            };
        }
    }
};
