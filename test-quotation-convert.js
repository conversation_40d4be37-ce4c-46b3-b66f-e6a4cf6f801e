// Test script to verify quotation to order conversion with deliveryNote
// This simulates the convert process and checks if deliveryNote is preserved

const _ = require('lodash');

// Simulate a quotation with deliveryNote in items
const sampleQuotation = {
    _id: 'quotation-123',
    status: 'draft',
    code: 'QUO-2024-001',
    partnerId: 'partner-123',
    currencyId: 'currency-123',
    quotationDate: new Date('2024-01-15'),
    expiryDate: new Date('2024-02-15'),
    items: [
        {
            id: 'item-1',
            productId: 'product-1',
            productCode: 'PROD-001',
            productDefinition: 'Test Product 1',
            quantity: 10,
            unitPrice: 100,
            deliveryNote: 'Özel teslimat talimatı - Kırılabilir',
            total: 1000
        },
        {
            id: 'item-2',
            productId: 'product-2',
            productCode: 'PROD-002',
            productDefinition: 'Test Product 2',
            quantity: 5,
            unitPrice: 200,
            deliveryNote: 'Soğuk zincir gerekli',
            total: 1000
        }
    ],
    subTotal: 2000,
    grandTotal: 2000,
    createdAt: new Date(),
    updatedAt: new Date()
};

console.log('=== QUOTATION TO ORDER CONVERSION TEST ===\n');

console.log('Original Quotation:');
console.log(`Code: ${sampleQuotation.code}`);
console.log(`Status: ${sampleQuotation.status}`);
console.log('Items with delivery notes:');
sampleQuotation.items.forEach((item, index) => {
    console.log(`  ${index + 1}. ${item.productCode}: "${item.deliveryNote}"`);
});

// Simulate the conversion process from convert-quotation.js
const convertedOrder = _.omit(sampleQuotation, [
    '_id',
    'status',
    'quotationDate',
    'expiryDate',
    'relatedDocuments',
    'revisionId',
    'revisionName',
    'hasRevisions',
    'createdAt',
    'updatedAt',
    'workflowApprovalStatus'
]);

// Set order-specific fields
convertedOrder.status = 'draft';
convertedOrder.code = 'ORD-2024-001';
convertedOrder.reference = sampleQuotation.code;
convertedOrder.orderDate = sampleQuotation.quotationDate;

console.log('\n=== CONVERSION RESULT ===\n');

console.log('Converted Order:');
console.log(`Code: ${convertedOrder.code}`);
console.log(`Status: ${convertedOrder.status}`);
console.log(`Reference: ${convertedOrder.reference}`);
console.log('Items with delivery notes:');

let allItemsHaveDeliveryNote = true;
convertedOrder.items.forEach((item, index) => {
    const hasDeliveryNote = item.hasOwnProperty('deliveryNote');
    if (!hasDeliveryNote) {
        allItemsHaveDeliveryNote = false;
    }
    console.log(`  ${index + 1}. ${item.productCode}: "${item.deliveryNote || 'MISSING'}"`);
});

console.log('\n=== TEST RESULTS ===\n');

if (allItemsHaveDeliveryNote) {
    console.log('✅ SUCCESS: All items have deliveryNote field preserved');
    console.log('✅ Convert işlemi teslimat notlarını koruyacak');
} else {
    console.log('❌ FAILED: Some items are missing deliveryNote field');
    console.log('❌ Convert işleminde teslimat notları kaybolacak');
}

// Additional checks
const originalItemCount = sampleQuotation.items.length;
const convertedItemCount = convertedOrder.items.length;

console.log(`\nItem count check: ${originalItemCount} -> ${convertedItemCount} ${originalItemCount === convertedItemCount ? '✅' : '❌'}`);

// Check if all original delivery notes are preserved
let deliveryNotesPreserved = true;
for (let i = 0; i < originalItemCount; i++) {
    const originalNote = sampleQuotation.items[i].deliveryNote;
    const convertedNote = convertedOrder.items[i].deliveryNote;
    if (originalNote !== convertedNote) {
        deliveryNotesPreserved = false;
        console.log(`❌ Delivery note mismatch for item ${i + 1}: "${originalNote}" -> "${convertedNote}"`);
    }
}

if (deliveryNotesPreserved) {
    console.log('✅ All delivery notes preserved exactly');
} else {
    console.log('❌ Some delivery notes were not preserved correctly');
}
